#!/usr/bin/env python3
"""
Test script for image analysis functionality using Google Gemini AI.
This script tests the core image handling features without the full GUI.
"""

import os
import io
import base64
from PIL import Image
from google import genai
from google.genai import types

# Test configuration
API_KEY = "YOUR_API_KEY_HERE"  # Replace with actual API key
TEST_IMAGE_PATH = "test_image.png"  # Path to test image

def initialize_gemini_client(api_key):
    """Initialize the Gemini AI client."""
    try:
        client = genai.Client(api_key=api_key)
        print("✅ Gemini client initialized successfully")
        return client
    except Exception as e:
        print(f"❌ Failed to initialize Gemini client: {e}")
        return None

def prepare_image_for_api(img):
    """Prepare image data for Gemini API."""
    try:
        # Resize image if too large (max 20MB for direct upload)
        max_size = (2048, 2048)  # Reasonable size limit
        if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # Convert to bytes
        img_byte_arr = io.BytesIO()
        img.save(img_byte_arr, format='JPEG', quality=85)
        img_bytes = img_byte_arr.getvalue()
        
        # Create Gemini API part
        return types.Part.from_bytes(data=img_bytes, mime_type='image/jpeg')
        
    except Exception as e:
        print(f"❌ Error preparing image for API: {e}")
        raise

def create_thumbnail(img):
    """Create a thumbnail for display."""
    try:
        # Create thumbnail
        thumbnail_size = (200, 200)
        img_copy = img.copy()
        img_copy.thumbnail(thumbnail_size, Image.Resampling.LANCZOS)
        
        # Convert to base64 for HTML display
        img_byte_arr = io.BytesIO()
        img_copy.save(img_byte_arr, format='JPEG', quality=80)
        img_base64 = base64.b64encode(img_byte_arr.getvalue()).decode('utf-8')
        
        return f"data:image/jpeg;base64,{img_base64}"
        
    except Exception as e:
        print(f"❌ Error creating thumbnail: {e}")
        return None

def analyze_image_with_gemini(client, image_data, user_context=""):
    """Analyze image using Gemini AI."""
    try:
        # Prepare the prompt
        if user_context.strip():
            prompt = f"Please analyze this image. User's specific question/context: {user_context}"
        else:
            prompt = "Please analyze this image in detail. Describe what you see, identify objects, text, people, and any other relevant information."
        
        # Create content for API call
        content = [prompt, image_data]
        
        # Generate response using the client
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=content
        )
        
        if response and hasattr(response, 'text') and response.text:
            return response.text.strip()
        else:
            return "No response generated from the model."
            
    except Exception as e:
        print(f"❌ Error analyzing image: {e}")
        return f"Error: {str(e)}"

def test_image_analysis():
    """Test the complete image analysis workflow."""
    print("🧪 Testing Image Analysis Functionality")
    print("=" * 50)
    
    # Check if test image exists
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"❌ Test image not found: {TEST_IMAGE_PATH}")
        print("Please create a test image or update TEST_IMAGE_PATH")
        return False
    
    # Initialize Gemini client
    client = initialize_gemini_client(API_KEY)
    if not client:
        return False
    
    try:
        # Load and process image
        print(f"📷 Loading image: {TEST_IMAGE_PATH}")
        with Image.open(TEST_IMAGE_PATH) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            print(f"✅ Image loaded successfully: {img.size} pixels, mode: {img.mode}")
            
            # Prepare image for API
            print("🔄 Preparing image for Gemini API...")
            image_data = prepare_image_for_api(img)
            print("✅ Image prepared for API")
            
            # Create thumbnail
            print("🖼️ Creating thumbnail...")
            thumbnail = create_thumbnail(img)
            if thumbnail:
                print("✅ Thumbnail created successfully")
            else:
                print("⚠️ Failed to create thumbnail")
            
            # Test image analysis
            print("🤖 Analyzing image with Gemini AI...")
            analysis_result = analyze_image_with_gemini(client, image_data, "What do you see in this image?")
            
            print("\n📋 Analysis Results:")
            print("-" * 30)
            print(analysis_result)
            print("-" * 30)
            
            return True
            
    except Exception as e:
        print(f"❌ Error during image analysis test: {e}")
        return False

def create_sample_image():
    """Create a simple test image if none exists."""
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"📝 Creating sample test image: {TEST_IMAGE_PATH}")
        try:
            # Create a simple colored rectangle
            img = Image.new('RGB', (400, 300), color='blue')
            img.save(TEST_IMAGE_PATH)
            print("✅ Sample image created")
        except Exception as e:
            print(f"❌ Failed to create sample image: {e}")

if __name__ == "__main__":
    print("🚀 Image Analysis Test Suite")
    print("=" * 50)
    
    # Check API key
    if API_KEY == "YOUR_API_KEY_HERE":
        print("❌ Please set your Gemini API key in the API_KEY variable")
        exit(1)
    
    # Create sample image if needed
    create_sample_image()
    
    # Run tests
    success = test_image_analysis()
    
    if success:
        print("\n✅ All tests passed! Image analysis functionality is working.")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
